using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Otg.Keycloak.Tenants.Application.Common.Interfaces.Apis;
using Otg.Keycloak.Tenants.Application.Common.Interfaces.Services;
using Otg.Keycloak.Tenants.Application.Dto;
using Otg.Keycloak.Tenants.Domain.Aggregates;

namespace Otg.Keycloak.Tenants.Infrastructure.KeyCloakApi.Services;

public class ClientService(IKeycloakService keycloakService) : IClientService
{
    public async Task<List<ClientDto>> GetClients(RealmRequest realmRequest)
    {
        var requestUri = $"{realmRequest.InstanceUrl}/admin/realms/{realmRequest.Realm}/clients";
        var result = await keycloakService.RequestWithToken<List<ClientDto>>(HttpMethod.Get, requestUri, realmRequest.Token);
        return result.Response;
    }

    public async Task<ClientDto?> GetClient(RealmRequest realmRequest, string clientName)
    {
        var clientList = await GetClients(realmRequest);

        var client =
            clientList.FirstOrDefault(n => string.Equals(n.Name, clientName, StringComparison.OrdinalIgnoreCase));

        return client;
    }

    public async Task<object> CreateClient(RealmRequest realmRequest, ClientDto clientDto)
    {
        var requestUri = $"{realmRequest.InstanceUrl}/admin/realms/{realmRequest.Realm}/clients";
        var result = await keycloakService.RequestWithToken<object>(HttpMethod.Post, requestUri, realmRequest.Token, JsonConvert.SerializeObject(clientDto));
        return result.Response;
    }

    public async Task<object> CreateClient(RealmRequest realmRequest, JObject clientData)
    {
        var requestUri = $"{realmRequest.InstanceUrl}/admin/realms/{realmRequest.Realm}/clients";
        var result = await keycloakService.RequestWithToken<object>(HttpMethod.Post, requestUri, realmRequest.Token, clientData.ToString());
        return result.Response;
    }

    public async Task<object> OverrideAuthenticationFlow(RealmRequest realmRequest, Guid clientId, OverrideAuthenticationFlowDto overrideAuthenticationFlowDto)
    {
        var requestUri = $"{realmRequest.InstanceUrl}/admin/realms/{realmRequest.Realm}/clients/{clientId}";
        var result = await keycloakService.RequestWithToken<object>(HttpMethod.Put, requestUri, realmRequest.Token, JsonConvert.SerializeObject(overrideAuthenticationFlowDto));
        return result.Response;
    }

    public async Task<string?> GetClientAudienceKey(RealmRequest realmRequest)
    {
        var requestUri = $"{realmRequest.InstanceUrl}/admin/realms/{realmRequest.Realm}";

        var httpResult = await keycloakService.RequestWithToken<Common.KeycloakRealm>(HttpMethod.Get, requestUri, realmRequest.Token);

        if (httpResult is { Success: false, Response.Attributes: not null }) return null;

        httpResult.Response.Attributes!.TryGetValue("audienceKey", out var audienceKeyValue);

        return audienceKeyValue;
    }

    public async Task<object> CreateProtocolMapper(RealmRequest realmRequest, Guid clientId, ProtocolMapperDto protocolMapperDto)
    {
        return await CreateProtocolMapper(realmRequest, clientId, JsonConvert.SerializeObject(protocolMapperDto));
    }

    public async Task<object> CreateProtocolMapper(RealmRequest realmRequest, Guid clientId, string mapperData)
    {
        var requestUri = $"{realmRequest.InstanceUrl}/admin/realms/{realmRequest.Realm}/clients/{clientId}/protocol-mappers/models";
        var result = await keycloakService.RequestWithToken<object>(HttpMethod.Post, requestUri, realmRequest.Token, mapperData);
        return result.Response;
    }

    public async Task AddRole(RealmRequest realmRequest, Guid clientId, AddRoleDto role)
    {
        var requestUri = $"{realmRequest.InstanceUrl}/admin/realms/{realmRequest.Realm}/clients/{clientId}/roles";

        await keycloakService.RequestWithToken<object>(HttpMethod.Post, requestUri, realmRequest.Token, JsonConvert.SerializeObject(role));
    }

    public async Task<string?> GetRealmAttribute(RealmRequest realmRequest, string attribute)
    {
        var requestUri = $"{realmRequest.InstanceUrl}/admin/realms/{realmRequest.Realm}";

        var httpResult = await keycloakService.RequestWithToken<Common.KeycloakRealm>(HttpMethod.Get, requestUri, realmRequest.Token);

        if (httpResult is { Success: false, Response.Attributes: not null }) return null;

        httpResult.Response.Attributes!.TryGetValue(attribute, out var attributeKeyValue);

        return attributeKeyValue;
    }

    public async Task DeleteClient(Guid? clientId, RealmRequest realmRequest)
    {
        var requestUri = $"{realmRequest.InstanceUrl}/admin/realms/{realmRequest.Realm}/clients/{clientId}";

        await keycloakService.RequestWithToken<object>(HttpMethod.Delete, requestUri, realmRequest.Token);
    }

    public async Task<object> UpdateClient(Guid? clientId, RealmRequest realmRequest, string clientData)
    {
        var requestUri = $"{realmRequest.InstanceUrl}/admin/realms/{realmRequest.Realm}/clients/{clientId}";
        var result = await keycloakService.RequestWithToken<object>(HttpMethod.Put, requestUri, realmRequest.Token, clientData);
        return result.Response;
    }
    
    public async Task<string?> GetServiceAccountUserId(RealmRequest realmRequest, Guid clientId)
    {
        var requestUri = $"{realmRequest.InstanceUrl}/admin/realms/{realmRequest.Realm}/clients/{clientId}/service-account-user";

        var httpResult = await keycloakService.RequestWithToken<JObject>(HttpMethod.Get, requestUri, realmRequest.Token);

        if (httpResult is { Success: false, Response: not null }) return null;

        return httpResult.Response?["id"]?.ToString();
    }
    
    public async Task<JObject?> GetClientRoleRepresentation(RealmRequest realmRequest, Guid clientId, string roleName)
    {
        var requestUri = $"{realmRequest.InstanceUrl}/admin/realms/{realmRequest.Realm}/clients/{clientId}/roles/{roleName}";

        var result = await keycloakService.RequestWithToken<JObject>(HttpMethod.Get, requestUri, realmRequest.Token);
        
        return result.Response;
    }

    public async Task AssignClientRoleToUser(RealmRequest realmRequest, Guid clientId, string userId, JObject role)
    {
        var requestUri = $"{realmRequest.InstanceUrl}/admin/realms/{realmRequest.Realm}/users/{userId}/role-mappings/clients/{clientId}";
        var rolesArray = new JArray { role };
        var result = await keycloakService.RequestWithToken<object>(HttpMethod.Post, requestUri, realmRequest.Token, rolesArray.ToString());
    }

}