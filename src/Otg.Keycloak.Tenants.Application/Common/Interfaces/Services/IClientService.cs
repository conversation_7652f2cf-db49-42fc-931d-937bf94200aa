using Newtonsoft.Json.Linq;
using Otg.Keycloak.Tenants.Application.Dto;
using Otg.Keycloak.Tenants.Domain.Aggregates;

namespace Otg.Keycloak.Tenants.Application.Common.Interfaces.Services;

public interface IClientService
{
    Task<object> CreateClient(RealmRequest realmRequest, ClientDto clientDto);

    Task<object> CreateClient(RealmRequest realmRequest, JObject clientData);

    Task<List<ClientDto>> GetClients(RealmRequest realmRequest);

    /// <summary>
    /// Returns a client by name.
    /// </summary>
    /// <param name="realmRequest">Request data</param>
    /// <param name="clientName">Client name (not id!)</param>
    /// <returns></returns>
    Task<ClientDto?> GetClient(RealmRequest realmRequest, string clientName);

    /// <summary>
    /// Sets the browser flow for the specified client
    /// </summary>
    /// <param name="realmRequest">Core request data</param>
    /// <param name="clientId">The internal Keycloak client id</param>
    /// <param name="overrideAuthenticationFlowDto">Override data</param>
    /// <returns></returns>
    Task<object> OverrideAuthenticationFlow(RealmRequest realmRequest, Guid clientId, OverrideAuthenticationFlowDto overrideAuthenticationFlowDto);

    /// <summary>
    /// Returns an audience key from realm properties - required
    /// </summary>
    /// <param name="realmRequest"></param>
    /// <returns></returns>
    Task<string?> GetClientAudienceKey(RealmRequest realmRequest);

    /// <summary>
    /// Creates a mapper against the dedicated client scope
    /// </summary>
    /// <param name="realmRequest">Core request data</param>
    /// <param name="clientId">The internal keycloak client id</param>
    /// <param name="protocolMapperDto">Mapper data</param>
    /// <returns></returns>
    Task<object> CreateProtocolMapper(RealmRequest realmRequest, Guid clientId, ProtocolMapperDto protocolMapperDto);

    /// <summary>
    /// Creates a mapper against the dedicated client scope
    /// </summary>
    /// <param name="realmRequest"></param>
    /// <param name="clientId"></param>
    /// <param name="mapperData"></param>
    /// <returns></returns>
    Task<object> CreateProtocolMapper(RealmRequest realmRequest, Guid clientId, string mapperData);

    /// <summary>
    /// Adds a client role to keycloak
    /// </summary>
    /// <param name="realmRequest">Core realm request data</param>
    /// <param name="clientId">Client Id (Guid) to add the role against</param>
    /// <param name="role">Role data</param>
    /// <returns></returns>
    Task AddRole(RealmRequest realmRequest, Guid clientId, AddRoleDto role);

    /// <summary>
    /// Retrieves the value of a realm attribute.
    /// </summary>
    /// <param name="realmRequest">Core realm request data</param>
    /// <param name="attribute">Name of the realm attribute</param>
    /// <returns></returns>
    Task<string?> GetRealmAttribute(RealmRequest realmRequest, string attribute);

    /// <summary>
    /// Deletes a client
    /// </summary>
    /// <param name="matchingClientId"></param>
    /// <param name="realmRequest"></param>
    /// <returns></returns>
    Task DeleteClient(Guid? matchingClientId, RealmRequest realmRequest);

    /// <summary>
    /// Updates a client
    /// </summary>
    /// <param name="clientId"></param>
    /// <param name="realmRequest"></param>
    /// <param name="clientData"></param>
    /// <returns></returns>
    Task<object> UpdateClient(Guid? clientId, RealmRequest realmRequest, string clientData);

    /// <summary>
    /// Returns the service account user id for a client
    /// </summary>
    /// <param name="realmRequest"></param>
    /// <param name="clientId"></param>
    /// <returns></returns>
    Task<string?> GetServiceAccountUserId(RealmRequest realmRequest, Guid clientId);

    /// <summary>
    /// Returns the full details of a role for a client
    /// </summary>
    /// <param name="realmRequest"></param>
    /// <param name="clientId"></param>
    /// <param name="roleName"></param>
    /// <returns></returns>
    Task<JObject?> GetClientRoleRepresentation(RealmRequest realmRequest, Guid clientId, string roleName);

    /// <summary>
    /// Assigns a client role to a user
    /// </summary>
    /// <param name="realmRequest"></param>
    /// <param name="clientId"></param>
    /// <param name="userId"></param>
    /// <param name="role"></param>
    /// <returns></returns>
    Task AssignClientRoleToUser(RealmRequest realmRequest, Guid clientId, string userId, JObject role);
}